class EVELINA:
    """
    Change data relating to the bot.
    """
    OWNER_IDS: list[int] = [ 
        1174797931577102512
    ]
    CLIENT_ID: int = 1182014901431062578

class LOGGING:
    """
    Change various logging variables.
    """
    LOGGING_GUILD: int = 1379433840556376165
    JOIN_LEAVE: int = 1379433840556376165
    REPORT: int = 1379433840556376165
    KEYS: int = 1379433840556376165
    MONEY: int = 1379433840556376165
    BLACKLIST: int = 1379433840556376165
    SYSTEM: int = 1379433840556376165
    FEEDBACK: int = 1379433840556376165
    LOGGING_READY: int = 1379433840556376165


class API:
    """
    Change various API keys.
    """
    EVELINA: str = ""
    RAPIDAPI: str = ""
    OMDB: str = ""
    GENIUS: str = ""
    LASTFM: str = [
        "572042e8c50931505cc992dd04db5f5e"
    ]
    OPENAI: str = [
        "********************************************************************************************************************************************************************"
    ]
    VALORANT: str = [
        "HDEV-8ba36b1d-fe8f-4411-b84d-1c2d9cf430e5"
    ]
    CLASHOFCLANS: str = [
        "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiIsImtpZCI6IjI4YTMxOGY3LTAwMDAtYTFlYi03ZmExLTJjNzQzM2M2Y2NhNSJ9.eyJpc3MiOiJzdXBlcmNlbGwiLCJhdWQiOiJzdXBlcmNlbGw6Z2FtZWFwaSIsImp0aSI6ImY0YTA4YmRmLTE4MWEtNDI3OS1hYzU0LWVlOGMxZDljNGYxOSIsImlhdCI6MTc0NTQzNzY1Niwic3ViIjoiZGV2ZWxvcGVyLzBiZThhMTFkLWZmZmUtNzQzMC1kZmZlLTZlZGY0MDUzZDkyNiIsInNjb3BlcyI6WyJjbGFzaCJdLCJsaW1pdHMiOlt7InRpZXIiOiJkZXZlbG9wZXIvc2lsdmVyIiwidHlwZSI6InRocm90dGxpbmcifSx7ImNpZHJzIjpbIjQ1LjExLjIzMS41NyIsIjQ1LjExLjIzMS41NiIsIjQ1LjExLjIzMS41NSIsIjQ1LjExLjIzMS41NCIsIjQ1LjExLjIzMS41MyJdLCJ0eXBlIjoiY2xpZW50In1dfQ.gMjR3p6pMI4NzpfiqXszHpQiaZTMoXjxjJ8z7PYMfYRyt9rYjiFj1AyBBINsNfUMMdBb5b9ntVg4pq6ymK_3ag"
    ]
    TEMPT: str = [
        "3BduE1OR97a55xU8Vg-IwfzXI4RoEaRXEHZxJ0Y_2fI"
    ]

class TWITCH:
    """
    Twitch API class.
    """
    TWITCH_CLIENT_ID: int = ""
    TWITCH_CLIENT_SECRET: str = ""


class CLOUDFLARE:
    """
    Change Cloudflare configuration settings.
    """
    R2_ENDPOINT_URL: str = "https://fb78d0d6cda0aeb7bc82ee893af69b6e.r2.cloudflarestorage.com"
    R2_ACCESS_KEY_ID: str = "cd035a962c6d534d456619a613dd16d1"
    R2_SECRET_ACCESS_KEY: str = "4721c751715211c8d24de5f7ca8bcd8919330c6cc3b7bec3d6e66bc81a404c63"

class POSTGRES:
    """
    Change database configuration settings.
    """
    HOST: str = "localhost"
    PORT: int = 5432
    DATABASE: str = "stunbot"
    USER: str = "stunuser"
    PASSWORD: str = "admin"

